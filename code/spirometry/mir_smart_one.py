# mir_smart_one.py

import asyncio
from mir_ble.device import MirSmartOneDevice
from mir_ble.exceptions import DeviceNotFoundError, ConnectionError, NotificationError, CommandError

DEVICE_ADDRESS = "" 


async def run_spirometry_test():
    device = MirSmartOneDevice(DEVICE_ADDRESS)

    try:
        print("🔍 Connecting to MIR Smart One...")
        await device.connect()

        print("🔔 Starting notifications...")
        await device.start_notifications()

        print("🚀 Enabling measurement mode...")
        await device.send_command(device.ENABLE_MEASUREMENT)

        print("\n💨 Please blow into the device now! Listening for 60 seconds...\n")
        await asyncio.sleep(60)

        print("🛑 Stopping notifications...")
        await device.stop_notifications()

    except DeviceNotFoundError as e:
        print(f"❌ Device not found: {e}")
    except ConnectionError as e:
        print(f"❌ Connection failed: {e}")
    except NotificationError as e:
        print(f"❌ Notification error: {e}")
    except CommandError as e:
        print(f"❌ Command sending error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        await device.disconnect()
        print("🔌 Disconnected.")

if __name__ == "__main__":
    asyncio.run(run_spirometry_test())
